import { FormType } from '@/types/public-order-status.types';

/**
 * Utility function to determine form type from URL parameters
 * @param params - URL search parameters
 * @returns FormType or undefined if no form type is specified
 */
export function getFormTypeFromParams(params: {
  [key: string]: string | string[] | undefined;
}): FormType | undefined {
  if (params.newImageRequest !== undefined) {
    return 'newImageRequest';
  } else if (params.customerContactNeeded !== undefined) {
    return 'customerContactNeeded';
  } else if (params.customerApproval !== undefined) {
    return 'customerApproval';
  }
  return undefined;
}

/**
 * Utility function to extract customer email from search parameters
 * @param params - URL search parameters
 * @returns Customer email string or undefined
 */
export function getCustomerEmailFromParams(params: {
  [key: string]: string | string[] | undefined;
}): string | undefined {
  return typeof params.customerEmail === 'string' ? params.customerEmail : undefined;
}

/**
 * Utility function to extract shopify order number from search parameters
 * @param params - URL search parameters
 * @returns Shopify order number string or undefined
 */
export function getShopifyOrderNumberFromParams(params: {
  [key: string]: string | string[] | undefined;
}): string | undefined {
  return typeof params.shopifyOrderNumber === 'string' ? params.shopifyOrderNumber : undefined;
}

/**
 * Utility function to extract line item ID from search parameters
 * @param params - URL search parameters
 * @returns Line item ID string or undefined
 */
export function getLineItemIdFromParams(params: {
  [key: string]: string | string[] | undefined;
}): string | undefined {
  return typeof params.lineItemId === 'string' ? params.lineItemId : undefined;
}

/**
 * Common error handler for order status pages
 * @param error - Error object
 * @returns User-friendly error message
 */
export function handleOrderStatusError(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  return 'Failed to fetch order status';
}
