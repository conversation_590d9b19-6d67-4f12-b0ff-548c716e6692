'use server'

import { PublicOrderStatus } from '@/types/public-order-status.types';
import { handlePublicApiError } from '@/utils/errorHandler';
import axios from 'axios';

const API_URL = process.env.NEXT_PUBLIC_API_URL;
const accessToken = process.env.NEXT_PUBLIC_CUSTOMER_VALIDATION_TOKEN;

function createPublicApiClient() {
  return axios.create({
    baseURL: API_URL,
    withCredentials: false,
    headers: {
      Authorization: `Bearer ${accessToken}`,
    },
  });
}

export async function fetchPublicOrderStatus(
  orderNumber: string,
  customerEmail?: string,
): Promise<PublicOrderStatus> {
  try {
    const publicApiClient = createPublicApiClient();
    const requestBody = {
      orderNumber: orderNumber,
      email: customerEmail || '',
    };
    const response = await publicApiClient.post('/order-tracking', requestBody);
    if (!response.data) {
      throw new Error('No data returned from server');
    }
    return response.data;
  } catch (error) {
    handlePublicApiError(error, 'fetch order status');
  }
}
