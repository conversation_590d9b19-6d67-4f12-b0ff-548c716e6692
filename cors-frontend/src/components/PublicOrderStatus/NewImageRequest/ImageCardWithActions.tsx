'use client';

import React from 'react';
import {
  Card,
  CardMedia,
  Typography,
  IconButton,
  Tooltip,
  LinearProgress,
  Box,
  Chip,
  Stack,
} from '@mui/material';
import { Visibility, Delete, CheckCircle, Error } from '@mui/icons-material';

interface ImageCardWithActionsProps {
  imageUrl: string;
  alt: string;
  fileName?: string;
  status?: string;
  uploadedAt?: string;
  isPreview?: boolean;
  onView?: () => void;
  onDelete?: () => void;
  showDelete?: boolean;
  showProgress?: boolean;
  progress?: boolean;
  fileSizeMB?: number;
  helperText?: string;
  variant?: 'rejected' | 'uploaded' | 'preview';
}

const ImageCardWithActions: React.FC<ImageCardWithActionsProps> = ({
  imageUrl,
  alt,
  fileName,
  status,
  uploadedAt,
  isPreview = false,
  onView,
  onDelete,
  showDelete = false,
  showProgress = false,
  progress = false,
  fileSizeMB,
  helperText,
  variant = 'preview',
}) => {
  const getVariantStyles = () => {
    switch (variant) {
      case 'rejected':
        return {
          borderColor: 'error.main',
          borderWidth: 2,
          borderStyle: 'solid',
        };
      case 'uploaded':
        return {
          borderColor: 'success.main',
          borderWidth: 2,
          borderStyle: 'solid',
        };
      default:
        return {
          borderColor: 'primary.main',
          borderWidth: isPreview ? 2 : 1,
          borderStyle: 'solid',
        };
    }
  };

  const getStatusIcon = () => {
    if (variant === 'rejected') return <Error color="error" fontSize="small" />;
    if (variant === 'uploaded') return <CheckCircle color="success" fontSize="small" />;
    return null;
  };

  const getStatusColor = () => {
    if (variant === 'rejected') return 'error';
    if (variant === 'uploaded') return 'success';
    return 'primary';
  };

  return (
    <Box sx={{ width: '100%', maxWidth: 320 }}>
      <Card
        sx={{
          borderRadius: 3,
          boxShadow: isPreview ? 8 : 3,
          transition: 'all 0.3s ease',
          cursor: onView ? 'pointer' : 'default',
          position: 'relative',
          overflow: 'hidden',
          ...getVariantStyles(),
          '&:hover': onView
            ? {
                boxShadow: 12,
                transform: 'translateY(-2px)',
              }
            : undefined,
        }}
        onClick={onView}
        tabIndex={onView ? 0 : -1}
        aria-label={onView ? 'View full image' : undefined}
      >
        {/* Status Badge */}
        {variant !== 'preview' && (
          <Box
            sx={{
              position: 'absolute',
              top: 8,
              left: 8,
              zIndex: 1,
            }}
          >
            <Chip
              icon={getStatusIcon()}
              label={variant === 'rejected' ? 'Rejected' : 'Uploaded'}
              size="small"
              color={getStatusColor()}
              sx={{
                fontWeight: 600,
                fontSize: '0.75rem',
              }}
            />
          </Box>
        )}

        <CardMedia
          component="img"
          height="220"
          image={imageUrl}
          alt={alt}
          sx={{
            objectFit: 'cover',
            width: '100%',
            transition: 'transform 0.3s ease',
            '&:hover': onView
              ? {
                  transform: 'scale(1.05)',
                }
              : undefined,
          }}
        />

        {/* Progress Overlay */}
        {showProgress && progress && (
          <Box
            sx={{
              position: 'absolute',
              bottom: 0,
              left: 0,
              right: 0,
              bgcolor: 'rgba(0, 0, 0, 0.7)',
              color: 'white',
              p: 1,
            }}
          >
            <Typography variant="caption" sx={{ mb: 1, display: 'block' }}>
              Uploading...
            </Typography>
            <LinearProgress color="primary" />
          </Box>
        )}
      </Card>

      {/* Image Details */}
      <Box sx={{ mt: 2 }}>
        {/* Action Buttons */}
        <Stack direction="row" spacing={1} sx={{ mb: 1 }}>
          {onView && (
            <Tooltip title="View full image" arrow>
              <IconButton
                color="primary"
                onClick={onView}
                aria-label="View full image"
                sx={{
                  bgcolor: 'primary.50',
                  '&:hover': { bgcolor: 'primary.100' },
                }}
              >
                <Visibility />
              </IconButton>
            </Tooltip>
          )}
          {showDelete && onDelete && (
            <Tooltip title="Delete image" arrow>
              <IconButton
                color="error"
                onClick={onDelete}
                aria-label="Delete image"
                sx={{
                  bgcolor: 'error.50',
                  '&:hover': { bgcolor: 'error.100' },
                }}
              >
                <Delete />
              </IconButton>
            </Tooltip>
          )}
        </Stack>

        {/* File Information */}
        {fileName && (
          <Typography
            variant="body2"
            color="text.primary"
            sx={{
              fontWeight: 500,
              mb: 0.5,
              wordBreak: 'break-word',
            }}
          >
            {fileName}
            {typeof fileSizeMB === 'number' && (
              <Typography component="span" variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                ({fileSizeMB.toFixed(2)} MB)
              </Typography>
            )}
          </Typography>
        )}

        {status && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
            <strong>Status:</strong> {status}
          </Typography>
        )}

        {uploadedAt && (
          <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
            <strong>Uploaded:</strong> {uploadedAt}
          </Typography>
        )}

        {helperText && (
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{
              fontStyle: 'italic',
              display: 'block',
              mt: 1,
              p: 1,
              borderRadius: 1,
            }}
          >
            {helperText}
          </Typography>
        )}
      </Box>
    </Box>
  );
};

export default ImageCardWithActions;
