'use client';

import React, { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Box,
  Button,
  Paper,
  Avatar,
  CardMedia,
  LinearProgress,
} from '@mui/material';
import { CloudUpload, Delete, BrokenImage, Visibility } from '@mui/icons-material';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';
import { toast } from 'react-toastify';
import { PublicOrderStatus } from '@/types/public-order-status.types';
import { UploadImage } from '@/actions/image-upload';
import { fetchRejectedImage, uploadNewImages } from '@/actions/public-order-status';
import { PublicLineItem } from '@/types/public-order-status.types';
import ImagePreviewDialog from '@/components/ImagePreviewDialog';
import Stack from '@mui/material/Stack';

interface NewImageRequestViewProps {
  orderData: PublicOrderStatus;
  formType?: string;
  lineItem?: PublicLineItem;
}

// Reusable image card with actions
interface ImageCardWithActionsProps {
  imageUrl: string;
  alt: string;
  fileName?: string;
  status?: string;
  uploadedAt?: string;
  isPreview?: boolean;
  onView?: () => void;
  onDelete?: () => void;
  showDelete?: boolean;
  showProgress?: boolean;
  progress?: boolean;
  fileSizeMB?: number;
  helperText?: string;
}

const ImageCardWithActions: React.FC<ImageCardWithActionsProps> = ({
  imageUrl,
  alt,
  fileName,
  status,
  uploadedAt,
  isPreview = false,
  onView,
  onDelete,
  showDelete = false,
  showProgress = false,
  progress = false,
  fileSizeMB,
  helperText,
}) => (
  <Stack spacing={1} alignItems="flex-start" sx={{ mb: 2, width: 1 }}>
    <Card
      sx={{
        maxWidth: 300,
        borderRadius: 2,
        boxShadow: isPreview ? 6 : 2,
        transition: 'box-shadow 0.2s',
        cursor: onView ? 'pointer' : 'default',
        '&:hover': onView ? { boxShadow: 12 } : undefined,
        width: '100%',
      }}
      onClick={onView}
      tabIndex={onView ? 0 : -1}
      aria-label={onView ? 'View full image' : undefined}
    >
      <CardMedia
        component="img"
        height="200"
        image={imageUrl}
        alt={alt}
        sx={{ objectFit: 'cover', width: '100%' }}
      />
    </Card>
    <Stack direction="row" spacing={1} alignItems="center">
      {onView && (
        <Tooltip title="View full image">
          <IconButton color="info" onClick={onView} aria-label="View full image">
            <Visibility />
          </IconButton>
        </Tooltip>
      )}
      {showDelete && onDelete && (
        <Tooltip title="Delete image">
          <IconButton color="error" onClick={onDelete} aria-label="Delete image">
            <Delete />
          </IconButton>
        </Tooltip>
      )}
    </Stack>
    {showProgress && progress && <LinearProgress sx={{ width: 1 }} />}
    {fileName && (
      <Typography variant="body2" color="text.secondary">
        {fileName}
        {typeof fileSizeMB === 'number' && ` (${fileSizeMB.toFixed(2)} MB)`}
      </Typography>
    )}
    {status && (
      <Typography variant="body2" color="text.secondary">
        Status: {status}
      </Typography>
    )}
    {uploadedAt && (
      <Typography variant="body2" color="text.secondary">
        Uploaded: {uploadedAt}
      </Typography>
    )}
    {helperText && (
      <Typography variant="caption" color="text.secondary">
        {helperText}
      </Typography>
    )}
  </Stack>
);

const NewImageRequestView: React.FC<NewImageRequestViewProps> = ({ orderData, lineItem }) => {
  const [uploadedImageUrl, setUploadedImageUrl] = useState<string | null>(null);
  const [uploading, setUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [rejectedImageData, setRejectedImageData] = useState<any>(null);
  const [uploadedImages, setUploadedImages] = useState<{
    [rejectedId: string]: { file: File | null; url: string | null; uploading: boolean };
  }>({});
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const effectiveLineItem = lineItem || (orderData.lineItems && orderData.lineItems[0]);

  useEffect(() => {
    async function fetchRejected() {
      if (
        orderData?.shopifyOrderNumber &&
        orderData?.customerEmail &&
        effectiveLineItem?.itemNumber
      ) {
        const data = await fetchRejectedImage(
          orderData.shopifyOrderNumber,
          orderData.customerEmail,
          effectiveLineItem.itemNumber,
        );
        setRejectedImageData(data);
        // Initialize uploadedImages state for each rejected image
        if (data?.images && data.images.length > 0) {
          const initialUploads: {
            [rejectedId: string]: { file: File | null; url: string | null; uploading: boolean };
          } = {};
          data.images.forEach((img: any) => {
            initialUploads[img.id] = { file: null, url: null, uploading: false };
          });
          setUploadedImages(initialUploads);
        }
      }
    }
    fetchRejected();
  }, [orderData?.shopifyOrderNumber, orderData?.customerEmail, effectiveLineItem?.itemNumber]);
  console.log('herere----', rejectedImageData);

  const handleFileChange = async (
    rejectedId: string,
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select an image file');
        return;
      }
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        toast.error('File size must be less than 10MB');
        return;
      }
      setUploadedImages(prev => ({
        ...prev,
        [rejectedId]: { ...prev[rejectedId], uploading: true, file, url: null },
      }));
      // Force a render before starting upload
      await new Promise(resolve => setTimeout(resolve, 0));
      try {
        const formData = new FormData();
        formData.append('file', file);
        const url = await UploadImage(formData);
        setUploadedImages(prev => ({
          ...prev,
          [rejectedId]: { ...prev[rejectedId], url, uploading: false },
        }));
      } catch (error) {
        toast.error('Failed to upload image');
        setUploadedImages(prev => ({
          ...prev,
          [rejectedId]: { ...prev[rejectedId], uploading: false, file: null, url: null },
        }));
      }
    }
  };

  const handleDeleteImage = (rejectedId: string) => {
    setUploadedImages(prev => ({
      ...prev,
      [rejectedId]: { ...prev[rejectedId], file: null, url: null },
    }));
  };

  console.log('here  is++++++++', uploadedImageUrl);
  const allUploaded =
    rejectedImageData &&
    rejectedImageData.images &&
    rejectedImageData.images.length > 0 &&
    rejectedImageData.images.every((img: any) => uploadedImages[img.id]?.url);

  const handleSubmit = async () => {
    if (!allUploaded) {
      toast.error('Please upload a new image for each rejected image');
      return;
    }
    setIsSubmitting(true);
    try {
      const uploads = rejectedImageData.images.map((img: any) => ({
        rejectedAttachmentId: img.id,
        imageUrl: uploadedImages[img.id].url,
      }));
      await uploadNewImages({
        orderNumber: orderData.shopifyOrderNumber,
        email: orderData.customerEmail,
        uploads,
      });
      toast.success('All images submitted successfully!');
      // Optionally reset state or close modal here
    } catch (error) {
      toast.error('Failed to submit images.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto' }}>
      {/* Header Card */}
      <Card
        elevation={3}
        sx={{
          mb: 3,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          color: 'white',
        }}
      >
        <CardContent sx={{ py: 3 }}>
          <Box display="flex" alignItems="center" gap={2} mb={2}>
            <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', width: 56, height: 56 }}>
              <BrokenImage fontSize="large" />
            </Avatar>
            <Box>
              <Typography variant="h4" component="h1" fontWeight="bold">
                New Image Request
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {(isSubmitting || uploading) && <LinearProgress sx={{ mb: 2 }} />}

      {/* Rejected Image Section */}
      {rejectedImageData && rejectedImageData.images && rejectedImageData.images.length > 0 && (
        <Card elevation={2} sx={{ mb: 3 }}>
          <CardContent>
            <Box display="flex" alignItems="center" gap={1} mb={2}>
              <Typography variant="h6" fontWeight="bold" color="error">
                Rejected Images
              </Typography>
            </Box>
            <Stack direction={{ xs: 'column', sm: 'row' }} spacing={3} flexWrap="wrap">
              {rejectedImageData.images.map((img: any, idx: number) => (
                <Box key={img.id} mb={4} minWidth={300}>
                  <Typography variant="body2" gutterBottom>
                    Image {idx + 1}
                  </Typography>
                  <ImageCardWithActions
                    imageUrl={img.url}
                    alt={img.filename}
                    fileName={img.filename}
                    status={img.status}
                    uploadedAt={new Date(img.uploadedAt).toLocaleDateString()}
                    onView={() => setPreviewImage(img.url)}
                    helperText="Rejected image"
                  />
                  <Box display="flex" alignItems="center" gap={1} mb={2} mt={2}>
                    <CloudUpload color="primary" />
                    <Typography variant="h6" fontWeight="bold">
                      Upload New Image
                    </Typography>
                  </Box>
                  <Box mt={2}>
                    {uploadedImages[img.id]?.url ? (
                      <ImageCardWithActions
                        imageUrl={uploadedImages[img.id].url || ''}
                        alt="Preview"
                        fileName={uploadedImages[img.id].file?.name}
                        fileSizeMB={uploadedImages[img.id].file ? (uploadedImages[img.id].file.size || 0) / 1024 / 1024 : undefined}
                        isPreview
                        onView={() => setPreviewImage(uploadedImages[img.id].url!)}
                        onDelete={() => handleDeleteImage(img.id)}
                        showDelete
                        showProgress={true}
                        progress={uploadedImages[img.id].uploading}
                        helperText="New image preview"
                      />
                    ) : (
                      <Paper
                        variant="outlined"
                        sx={{
                          p: 3,
                          textAlign: 'center',
                          borderStyle: 'dashed',
                          borderWidth: 2,
                          borderColor: uploadedImages[img.id]?.uploading ? 'primary.main' : 'grey.300',
                          borderRadius: 2,
                          cursor: uploadedImages[img.id]?.uploading ? 'not-allowed' : 'pointer',
                          opacity: uploadedImages[img.id]?.uploading ? 0.6 : 1,
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            borderColor: uploadedImages[img.id]?.uploading ? 'grey.300' : 'primary.main',
                            bgcolor: uploadedImages[img.id]?.uploading ? undefined : 'primary.50',
                          },
                        }}
                        onClick={() =>
                          !uploadedImages[img.id]?.uploading &&
                          document.getElementById(`file-input-${img.id}`)?.click()
                        }
                      >
                        <input
                          id={`file-input-${img.id}`}
                          type="file"
                          accept="image/*"
                          onChange={e => handleFileChange(img.id, e)}
                          style={{ display: 'none' }}
                          disabled={uploadedImages[img.id]?.uploading}
                        />
                        <CloudUpload sx={{ fontSize: 48, color: 'grey.400', mb: 1 }} />
                        <Typography variant="h6" gutterBottom>
                          Drop your image here
                        </Typography>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          or click to browse files
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          Supports: JPG, PNG, GIF (Max 10MB)
                        </Typography>
                        {uploadedImages[img.id]?.uploading && <LinearProgress sx={{ mt: 2, width: 1 }} />}
                      </Paper>
                    )}
                  </Box>
                </Box>
              ))}
            </Stack>
          </CardContent>
        </Card>
      )}

      {/* Submit Button */}
      <Box textAlign="center">
        <Button
          variant="contained"
          size="large"
          onClick={handleSubmit}
          disabled={
            isSubmitting || !allUploaded || Object.values(uploadedImages).some(u => u.uploading)
          }
          sx={{
            minWidth: 200,
            py: 1.5,
            borderRadius: 2,
            fontSize: '1.1rem',
          }}
        >
          {isSubmitting ? 'Submitting...' : 'Submit Request'}
        </Button>
      </Box>
      <ImagePreviewDialog
        open={!!previewImage}
        onClose={() => setPreviewImage(null)}
        imageUrl={previewImage}
        title="Image Preview"
      />
    </Box>
  );
};

export default NewImageRequestView;
