'use client';

import React from 'react';
import { Dialog, DialogContent, IconButton, Box } from '@mui/material';
import { Close } from '@mui/icons-material';
import NewImageRequestView from './NewImageRequestView';
import { PublicOrderStatus, PublicLineItem } from '@/types/public-order-status.types';

interface NewImageRequestModalProps {
  open: boolean;
  onClose: () => void;
  orderData: PublicOrderStatus;
  lineItem: PublicLineItem;
}

const NewImageRequestModal: React.FC<NewImageRequestModalProps> = ({
  open,
  onClose,
  orderData,
  lineItem,
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          borderRadius: 2,
          maxHeight: '90vh',
        },
      }}
    >
      <Box sx={{ position: 'relative' }}>
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            zIndex: 1,
            '&:hover': {
              bgcolor: 'rgba(255, 255, 255, 0.9)',
            },
          }}
        >
          <Close />
        </IconButton>

        <DialogContent sx={{ p: 0, overflow: 'hidden' }}>
          <NewImageRequestView orderData={orderData} lineItem={lineItem} formType="newImageRequest" />
        </DialogContent>
      </Box>
    </Dialog>
  );
};

export default NewImageRequestModal;
