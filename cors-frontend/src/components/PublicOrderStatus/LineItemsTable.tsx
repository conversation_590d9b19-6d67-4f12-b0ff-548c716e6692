'use client';
import React, { useState } from 'react';
import {
  Card,
  CardContent,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Box,
  Alert,
  Chip,
  Button,
} from '@mui/material';
import { Inventory2, ImageSearch } from '@mui/icons-material';
import PublicStatusChip from './PublicStatusChip';
import NewImageRequestModal from './NewImageRequest/NewImageRequestModal';
import { PublicLineItem, PublicOrderStatus } from '@/types/public-order-status.types';

interface LineItemsTableProps {
  lineItems: PublicLineItem[];
  orderData: PublicOrderStatus;
}

const LineItemsTable: React.FC<LineItemsTableProps> = ({ lineItems, orderData }) => {
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedLineItem, setSelectedLineItem] = useState<PublicLineItem | null>(null);

  const handleNewImageRequest = (lineItem: PublicLineItem) => {
    setSelectedLineItem(lineItem);
    setModalOpen(true);
  };

  const handleCloseModal = () => {
    setModalOpen(false);
    setSelectedLineItem(null);
  };

  const shouldShowNewImageRequestButton = (status: string) => {
    return status === 'Awaiting Customer Response';
  };
  if (!lineItems || lineItems.length === 0) {
    return (
      <Card elevation={2}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Order Items
          </Typography>
          <Alert severity="info">No items found for this order.</Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card elevation={2}>
        <CardContent>
          <Box display="flex" alignItems="center" gap={1} mb={3}>
            <Inventory2 color="primary" />
            <Typography variant="h6" fontWeight="bold">
              Order Line Items ({lineItems.length})
            </Typography>
          </Box>

          <TableContainer component={Paper} variant="outlined">
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell sx={{ fontWeight: 'bold' }}>Item #</TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Priority</TableCell>
                  <TableCell align="center" sx={{ fontWeight: 'bold' }}>
                    Quantity
                  </TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>
                    <Box display="flex" alignItems="center" gap={1}>
                      Current Status
                    </Box>
                  </TableCell>
                  <TableCell sx={{ fontWeight: 'bold' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {lineItems.map(item => (
                  <TableRow key={item.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight={500}>
                        {item.itemNumber || item.id.slice(-8)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={item.priority || 'Standard'}
                        size="small"
                        color={
                          item.priority === 'Pajama Rush'
                            ? 'warning'
                            : item.priority === 'Standard'
                              ? 'error'
                              : 'default'
                        }
                        variant="filled"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Typography variant="body2">{item.quantity}</Typography>
                    </TableCell>
                    <TableCell>
                      <PublicStatusChip status={item.status} variant="lineItem" size="small" />
                    </TableCell>
                    <TableCell>
                      {shouldShowNewImageRequestButton(item.status) && (
                        <Button
                          variant="contained"
                          size="small"
                          startIcon={<ImageSearch />}
                          onClick={() => handleNewImageRequest(item)}
                          sx={{
                            borderRadius: 1,
                            textTransform: 'none',
                            fontSize: '0.75rem',
                          }}
                        >
                          New Image Request
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      {/* New Image Request Modal */}
      {modalOpen && selectedLineItem && (
        <NewImageRequestModal
          open={modalOpen}
          onClose={handleCloseModal}
          orderData={orderData}
          lineItem={selectedLineItem}
        />
      )}
    </>
  );
};

export default LineItemsTable;
