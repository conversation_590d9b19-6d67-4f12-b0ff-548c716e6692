import {
  Typo<PERSON>,
  Box,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton,
  Tooltip,
} from '@mui/material/';
import Grid2 from '@mui/material/Grid2';
import CloseIcon from '@mui/icons-material/Close';
import ItemNotesTabs from './ItemNotesTabs';
import StatusChip from '@/components/StatusChip';
import { OrderDetailsDialogProps } from '@/types/order-details.types';
import { useEffect, useState } from 'react';
import { useCopyToClipboard } from 'react-use';

const OrderDetailsDialog = ({
  open,
  onClose,
  orderData,
  itemNotes,
  setItemNotes,
}: OrderDetailsDialogProps) => {
  const [copyTooltip, setCopyTooltip] = useState('Copy order status URL');
  const [, copyToClipboard] = useCopyToClipboard();

  useEffect(() => {
    if (orderData?.lineItems) {
      const initialNotes: Record<string, string[]> = {};
      orderData.lineItems.forEach((item: any) => {
        if (item.id && item.notes) {
          initialNotes[item.id] = item.notes;
        }
      });
      setItemNotes(initialNotes);
    }
  }, [orderData, setItemNotes]);

  // Generate order status URL
  const generateOrderStatusUrl = () => {
    if (!orderData?.shopifyOrderNumber || !orderData?.customerEmail) {
      return '';
    }

    // Get the current origin if available, otherwise fallback to environment-based URL
    const baseUrl =
      typeof window !== 'undefined'
        ? window.location.origin
        : process.env.NODE_ENV === 'production'
          ? 'https://cors-dev.cuddleclones.com'
          : 'http://localhost:3000';

    return `${baseUrl}/order-status?shopifyOrderNumber=${orderData.shopifyOrderNumber}&customerEmail=${encodeURIComponent(orderData.customerEmail)}`;
  };

  const handleCopyUrl = () => {
    const url = generateOrderStatusUrl();
    if (url) {
      copyToClipboard(url);
      setCopyTooltip('Copied!');
      setTimeout(() => setCopyTooltip('Copy order status URL'), 2000);
    }
  };

  const handleViewUrl = () => {
    const url = generateOrderStatusUrl();
    if (url) {
      window.open(url, '_blank');
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="md"
      sx={{
        '& .MuiDialog-paper': {
          overflow: 'visible',
        },
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          {/* Changed from Typography variant="h6" to span to avoid nesting h-tags */}
          <span>Order Details - #{orderData?.shopifyOrderNumber}</span>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent dividers>
        <Box sx={{ mb: 3 }}>
          {/* Use component="div" to avoid nesting h-tags */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
            <Typography variant="h6" component="div">
              Order Status
            </Typography>
            {orderData?.shopifyOrderNumber && orderData?.customerEmail && (
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Tooltip title={copyTooltip} arrow>
                  <IconButton
                    size="small"
                    onClick={handleCopyUrl}
                    sx={{
                      color: 'primary.main',
                      '&:hover': {
                        backgroundColor: 'primary.light',
                        color: 'primary.contrastText',
                      },
                    }}
                  >
                    <i className="ri-file-copy-line" style={{ fontSize: '18px' }} />
                  </IconButton>
                </Tooltip>
                <Tooltip title="View order status page" arrow>
                  <IconButton
                    size="small"
                    onClick={handleViewUrl}
                    sx={{
                      color: 'primary.main',
                      '&:hover': {
                        backgroundColor: 'primary.light',
                        color: 'primary.contrastText',
                      },
                    }}
                  >
                    <i className="ri-external-link-line" style={{ fontSize: '18px' }} />
                  </IconButton>
                </Tooltip>
              </Box>
            )}
          </Box>
          <StatusChip status={orderData?.orderStatus} />
        </Box>

        <Grid2 container spacing={4}>
          <Grid2 size={{ xs: 12, md: 6 }}>
            <Typography variant="h6" component="div" gutterBottom>
              Customer Shipping Address
            </Typography>
            <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
              <Typography>
                {orderData?.shippingAddress?.first_name +
                  ' ' +
                  orderData?.shippingAddress?.last_name}
              </Typography>
              <Typography>{orderData?.shippingAddress?.phone}</Typography>

              <Typography>
                {orderData?.shippingAddress?.address1 + ' ' + orderData?.shippingAddress?.address2}
              </Typography>
              <Typography>
                {orderData?.shippingAddress?.city}, {orderData?.shippingAddress?.province}{' '}
                {orderData?.shippingAddress?.zip}
              </Typography>
              <Typography>{orderData?.shippingAddress?.company}</Typography>

              <Typography>{orderData?.shippingAddress?.country}</Typography>
            </Paper>
          </Grid2>

          <Grid2 size={{ xs: 12, md: 6 }}>
            <Typography variant="h6" component="div" gutterBottom>
              Customer Billing Address
            </Typography>
            <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
              <Typography>
                {orderData?.billingAddress?.first_name + ' ' + orderData?.billingAddress?.last_name}
              </Typography>
              <Typography>{orderData?.billingAddress?.phone}</Typography>
              <Typography>
                {orderData?.billingAddress?.address1 + ' ' + orderData?.billingAddress?.address2}
              </Typography>
              <Typography>
                {orderData?.billingAddress?.city}, {orderData?.billingAddress?.province}{' '}
                {orderData?.billingAddress?.zip}
              </Typography>
              <Typography>{orderData?.billingAddress?.company}</Typography>
              <Typography>{orderData?.billingAddress?.country}</Typography>
            </Paper>
          </Grid2>

          <Grid2 size={{ xs: 12 }}>
            <Typography variant="h6" component="div" gutterBottom>
              Payment Information
            </Typography>
            <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
              <Grid2 container spacing={2}>
                <Grid2 size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2">Discount</Typography>
                  <Typography>{orderData?.paymentInformation?.discount}</Typography>
                </Grid2>
                <Grid2 size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2">Shipping Price</Typography>
                  <Typography>{orderData?.paymentInformation?.shipping_price}</Typography>
                </Grid2>
                <Grid2 size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2">SubTotal Price</Typography>
                  <Typography>{orderData?.paymentInformation?.subtotal_price}</Typography>
                </Grid2>
                <Grid2 size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2">Tax</Typography>
                  <Typography>{orderData?.paymentInformation?.tax}</Typography>
                </Grid2>
                <Grid2 size={{ xs: 12, md: 6 }}>
                  <Typography variant="subtitle2">Total Amount</Typography>
                  <Typography>${orderData?.paymentInformation?.total_price}</Typography>
                </Grid2>
              </Grid2>
            </Paper>
          </Grid2>

          <Grid2 size={{ xs: 12 }}>
            <Typography variant="h6" component="div" gutterBottom>
              Item SKUs and Quantities
            </Typography>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Item #</TableCell>
                    <TableCell>SKU</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                    <TableCell>Fulfillment Status</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {orderData?.lineItems &&
                    orderData.lineItems.map((item: any) => (
                      <TableRow key={item.id}>
                        <TableCell>{item?.itemNumber}</TableCell>
                        <TableCell>{item?.productSku?.sku || 'Not Provided'}</TableCell>
                        <TableCell align="right">{item.quantity}</TableCell>
                        <TableCell>
                          <StatusChip status={orderData?.orderStatus} />
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Grid2>

          <Grid2 size={{ xs: 12 }}>
            <Typography variant="h6" component="div" gutterBottom>
              Notes
            </Typography>
            <Paper sx={{ p: 2, bgcolor: 'background.default' }}>
              <ItemNotesTabs
                orderData={{
                  id: orderData?.id,
                  orderNumber: orderData?.shopifyOrderNumber,
                  items: orderData?.lineItems?.map((item: any) => ({
                    id: item?.id,
                    name: item?.productSku?.sku,
                    itemNumber: item?.itemNumber,
                    status: item?.status,
                    cancelReason: item?.cancelReason
                      ? item.cancelReason
                      : {
                          reason: item.cancelReason?.reason,
                          timestamp: item?.cancelReason?.timestamp,
                          username: item.cancelReason?.username,
                        },
                  })),
                }}
                itemNotes={itemNotes}
                setItemNotes={setItemNotes}
              />
            </Paper>
          </Grid2>
        </Grid2>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default OrderDetailsDialog;
